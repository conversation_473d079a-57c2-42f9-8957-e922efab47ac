const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require("electron");

// 安全地暴露 API 给渲染进程
// contextBridge.exposeInMainWorld("electronAPI", {
//   // 发送消息到主进程
//   sendToMain: (msg) => ipcRenderer.send("to-main", msg),

//   // 接收主进程消息
//   onFromMain: (callback) =>
//     ipcRenderer.on("from-main", (event, data) => callback(data)),

//   // 获取版本信息
//   getVersions: () => ipcRenderer.invoke("get-versions"),
// });

contextBridge.exposeInMainWorld("sdk", {
  readFile: (options) => ipcRenderer.invoke("read-file", options),
  openUrl: (options) => ipcRenderer.invoke("open-url", options),
});

console.log("preload脚本已加载");
