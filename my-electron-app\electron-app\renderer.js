console.log("渲染进程已加载");

// 在页面加载完成后执行
window.addEventListener("DOMContentLoaded", () => {
  // 获取版本信息并显示
  window.electronAPI.getVersions().then((versions) => {
    const versionInfo = `
      <p>Node.js: ${versions.node}</p>
      <p>Chromium: ${versions.chrome}</p>
      <p>Electron: ${versions.electron}</p>
    `;
    document.body.innerHTML += versionInfo;
  });

  // 调用通信API
  window.electronAPI.sendToMain("Hello from renderer");

  window.electronAPI.onFromMain((data) => {
    console.log("收到主进程消息:", data);
  });
});
