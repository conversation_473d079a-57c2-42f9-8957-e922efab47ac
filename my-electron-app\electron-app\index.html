<!DOCTYPE html>
<html>
  <body>
    <button onclick="testReadFile()">读取文件</button>
    <button onclick="testOpenUrl()">打开网页</button>
    <script>
      async function testReadFile() {
        const result = await window.sdk.readFile({
          filePath: "test.txt",
          range: [0, 1024], // 读取前 1KB
        });
        console.log(result);
      }

      function testOpenUrl() {
        window.sdk.openUrl({
          url: "https://example.com",
          options: { type: "browserwindow" },
        });
      }
    </script>
  </body>
</html>
